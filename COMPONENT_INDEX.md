# LibyanoEx Web App - Component Index

## 📍 Quick Navigation Guide

This document provides a comprehensive index of all components, pages, and features in the LibyanoEx shipping forwarding web application. Use this as your roadmap to understand and navigate the codebase.

## 🏠 Main Pages

### Home & Authentication
| Component | Location | Purpose | Status |
|-----------|----------|---------|---------|
| **Home Page** | `src/app/page.tsx` | Landing page with auth options | ✅ Complete |
| **Login Page** | `src/app/auth/login/page.tsx` | User authentication | ✅ Complete |
| **Register Page** | `src/app/auth/register/page.tsx` | User registration | 🔄 Needs Update |
| **Forgot Password** | `src/app/auth/forgot-password/page.tsx` | Password recovery | ❌ To Create |

### Dashboard & Main Features
| Component | Location | Purpose | Status |
|-----------|----------|---------|---------|
| **Dashboard** | `src/app/dashboard/page.tsx` | Main user dashboard | 🔄 Needs Update |
| **Packages** | `src/app/packages/page.tsx` | Package management | ❌ To Create |
| **Shipping Calculator** | `src/app/shipping/page.tsx` | Rate calculations | ❌ To Create |
| **Address Book** | `src/app/addresses/page.tsx` | Address management | ❌ To Create |
| **Account Settings** | `src/app/account/page.tsx` | User preferences | ❌ To Create |

### Admin Features
| Component | Location | Purpose | Status |
|-----------|----------|---------|---------|
| **Admin Dashboard** | `src/app/admin/page.tsx` | Admin overview | 🔄 Needs Update |
| **User Management** | `src/app/users/page.tsx` | Manage customers | 🔄 Needs Update |
| **Reports** | `src/app/reports/page.tsx` | Analytics & reports | 🔄 Needs Update |

## 🧩 Reusable Components

### UI Components (`src/components/ui/`)
| Component | Purpose | Usage |
|-----------|---------|-------|
| **Button** | Primary action buttons | `<Button>Click me</Button>` |
| **Input** | Form input fields | `<Input placeholder="Enter text" />` |
| **Card** | Content containers | `<Card><CardContent>...</CardContent></Card>` |
| **Label** | Form labels | `<Label htmlFor="input">Label</Label>` |
| **Checkbox** | Boolean inputs | `<Checkbox checked={true} />` |

### Feature Components (To Be Created)

#### Authentication Components (`src/components/auth/`)
| Component | Purpose | Status |
|-----------|---------|---------|
| **LoginForm** | Reusable login form | ❌ To Create |
| **RegisterForm** | Multi-step registration | ❌ To Create |
| **PasswordReset** | Password recovery form | ❌ To Create |
| **AuthGuard** | Route protection | ❌ To Create |

#### Shipping Components (`src/components/shipping/`)
| Component | Purpose | Status |
|-----------|---------|---------|
| **PackageCard** | Display package info | ❌ To Create |
| **ShippingCalculator** | Rate calculation widget | ❌ To Create |
| **TrackingDisplay** | Package tracking info | ❌ To Create |
| **AddressForm** | Address input form | ❌ To Create |

#### Dashboard Components (`src/components/dashboard/`)
| Component | Purpose | Status |
|-----------|---------|---------|
| **StatsCard** | Dashboard statistics | ❌ To Create |
| **RecentPackages** | Recent package list | ❌ To Create |
| **QuickActions** | Common action buttons | ❌ To Create |
| **NotificationPanel** | User notifications | ❌ To Create |

## 🗂️ Feature Modules (`src/features/`)

### Authentication Module
| File | Purpose | Status |
|------|---------|---------|
| `authentication/hooks/useAuth.ts` | Auth state management | ❌ To Create |
| `authentication/types/auth.types.ts` | Auth type definitions | ❌ To Create |
| `authentication/utils/validation.ts` | Auth validation logic | ❌ To Create |

### Package Management Module
| File | Purpose | Status |
|------|---------|---------|
| `packages/hooks/usePackages.ts` | Package state management | ❌ To Create |
| `packages/types/package.types.ts` | Package type definitions | ❌ To Create |
| `packages/utils/packageHelpers.ts` | Package utility functions | ❌ To Create |

### Shipping Module
| File | Purpose | Status |
|------|---------|---------|
| `shipping/hooks/useShipping.ts` | Shipping calculations | ❌ To Create |
| `shipping/types/shipping.types.ts` | Shipping type definitions | ❌ To Create |
| `shipping/utils/rateCalculator.ts` | Rate calculation logic | ❌ To Create |

## 🔧 Utilities & Libraries (`src/lib/`)

### Database & API
| File | Purpose | Status |
|------|---------|---------|
| `supabase/client.ts` | Database client | ✅ Exists |
| `supabase/types.ts` | Database types | ✅ Exists |
| `auth.ts` | Authentication helpers | ✅ Exists |

### Validation & Utils
| File | Purpose | Status |
|------|---------|---------|
| `validations/auth.schemas.ts` | Auth form validation | ❌ To Create |
| `validations/package.schemas.ts` | Package form validation | ❌ To Create |
| `utils/formatters.ts` | Data formatting helpers | ❌ To Create |
| `utils/constants.ts` | App constants | ❌ To Create |

## 🎨 Styling & Assets

### Styles
| File | Purpose | Status |
|------|---------|---------|
| `src/app/globals.css` | Global styles | ✅ Complete |
| `tailwind.config.js` | Tailwind configuration | ✅ Complete |

### Icons & Assets
- **Lucide React Icons**: Package, Truck, Globe, Shield, Clock, MapPin
- **Company Logo**: Package icon as brand symbol
- **Static Assets**: `public/` directory for images

## 🗄️ Database Schema (`database/`)

| File | Purpose | Status |
|------|---------|---------|
| `schema.sql` | Database structure | ✅ Exists |
| `functions.sql` | Database functions | ✅ Exists |
| `rls-policies.sql` | Security policies | ✅ Exists |
| `seed-data.sql` | Sample data | ✅ Exists |

## 📱 State Management (`src/stores/`)

### Zustand Stores (To Be Created)
| Store | Purpose | Status |
|-------|---------|---------|
| `authStore.ts` | User authentication state | ❌ To Create |
| `packageStore.ts` | Package management state | ❌ To Create |
| `shippingStore.ts` | Shipping calculations state | ❌ To Create |
| `notificationStore.ts` | App notifications state | ❌ To Create |

## 🔍 How to Find Components

### By Feature
- **Authentication**: Look in `src/app/auth/` and `src/components/auth/`
- **Package Management**: Look in `src/app/packages/` and `src/components/shipping/`
- **Dashboard**: Look in `src/app/dashboard/` and `src/components/dashboard/`
- **Admin Features**: Look in `src/app/admin/` and `src/app/users/`

### By Type
- **Pages**: `src/app/[feature]/page.tsx`
- **Components**: `src/components/[feature]/ComponentName.tsx`
- **Utilities**: `src/lib/[category]/fileName.ts`
- **Types**: `src/types/[feature].types.ts`

## 🚀 Next Development Steps

1. **Complete Authentication Flow** - Enhance registration with multi-step process
2. **Create Package Management** - Build package tracking and management features
3. **Implement Shipping Calculator** - Add rate calculation functionality
4. **Build Admin Dashboard** - Create comprehensive admin tools
5. **Add Address Management** - Implement address book functionality

## 📞 Quick Reference

- **Main Entry Point**: `src/app/page.tsx`
- **Authentication**: `src/app/auth/login/page.tsx`
- **Project Structure**: `PROJECT_STRUCTURE.md`
- **Component Index**: This file (`COMPONENT_INDEX.md`)

---

This index will be updated as new components are added to the system. Use it as your navigation guide when working with the LibyanoEx shipping forwarding application.
