'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createSupabaseBrowserClient } from '@/lib/supabase'
import { useAuthStore } from '@/stores/auth-store'
import { User } from '@/types'

export function useAuth() {
  const { user, isLoading, isAuthenticated, setUser, setLoading, logout } = useAuthStore()

  // Check if Supabase is properly configured
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  const isSupabaseConfigured = supabaseUrl && supabaseAnonKey &&
    supabaseUrl !== 'your_supabase_project_url' &&
    supabaseAnonKey !== 'your_supabase_anon_key'

  useEffect(() => {
    // If Supabase is not configured, set a mock user for development
    if (!isSupabaseConfigured) {
      const mockUser = {
        id: 'dev-user-123',
        email: '<EMAIL>',
        full_name: 'Development User',
        avatar_url: null,
        role: 'admin' as const,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true,
      }
      setUser(mockUser)
      return
    }

    const supabase = createSupabaseBrowserClient()

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Error getting session:', error)
          setLoading(false)
          return
        }

        if (session?.user) {
          await fetchUserProfile(session.user.id)
        } else {
          setUser(null)
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error)
        setUser(null)
      }
    }

    if (isSupabaseConfigured) {
      getInitialSession()

      // Listen for auth changes
      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        async (event, session) => {
          if (event === 'SIGNED_IN' && session?.user) {
            await fetchUserProfile(session.user.id)
          } else if (event === 'SIGNED_OUT') {
            setUser(null)
          }
        }
      )

      return () => {
        subscription.unsubscribe()
      }
    }
  }, [isSupabaseConfigured, setUser, setLoading])

  const fetchUserProfile = async (userId: string) => {
    if (!isSupabaseConfigured) return

    try {
      const supabase = createSupabaseBrowserClient()
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching profile:', error)
        setUser(null)
        return
      }

      const userData: User = {
        id: profile.id,
        email: profile.email,
        full_name: profile.full_name,
        avatar_url: profile.avatar_url,
        role: profile.role,
        created_at: profile.created_at,
        updated_at: profile.updated_at,
        is_active: profile.is_active,
      }

      setUser(userData)
    } catch (error) {
      console.error('Error in fetchUserProfile:', error)
      setUser(null)
    }
  }

  const signOut = async () => {
    try {
      if (isSupabaseConfigured) {
        const supabase = createSupabaseBrowserClient()
        await supabase.auth.signOut()
      }
      logout()
    } catch (error) {
      console.error('Error signing out:', error)
      logout() // Still logout locally even if Supabase fails
    }
  }

  return {
    user,
    isLoading,
    isAuthenticated,
    signOut,
  }
}

export function useRequireAuth(redirectTo: string = '/auth/login') {
  const { user, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !user) {
      router.push(redirectTo)
    }
  }, [user, isLoading, router, redirectTo])

  return { user, isLoading }
}

export function useRequireRole(allowedRoles: string[], redirectTo: string = '/') {
  const { user, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && user && !allowedRoles.includes(user.role)) {
      router.push(redirectTo)
    }
  }, [user, isLoading, allowedRoles, router, redirectTo])

  return { user, isLoading, hasAccess: user ? allowedRoles.includes(user.role) : false }
}
