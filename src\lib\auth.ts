import { createSupabaseServerClient } from './supabase-server'
import { createSupabaseBrowserClient } from './supabase'
import { User } from '@/types'

// Server-side auth functions
export async function getUser(): Promise<User | null> {
  const supabase = createSupabaseServerClient()

  // If Supabase is not configured, return a mock user for development
  if (!supabase) {
    return {
      id: 'dev-user-123',
      email: '<EMAIL>',
      full_name: 'Development User',
      avatar_url: null,
      role: 'admin',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_active: true,
    }
  }

  try {
    const { data: { user }, error } = await supabase.auth.getUser()

    if (error || !user) {
      return null
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return null
    }

    return {
      id: profile.id,
      email: profile.email,
      full_name: profile.full_name,
      avatar_url: profile.avatar_url,
      role: profile.role,
      created_at: profile.created_at,
      updated_at: profile.updated_at,
      is_active: profile.is_active,
    }
  } catch (error) {
    console.error('Error getting user:', error)
    return null
  }
}

export async function signOut() {
  const supabase = createSupabaseServerClient()
  if (supabase) {
    await supabase.auth.signOut()
  }
}

// Client-side auth functions
export async function signInWithEmail(email: string, password: string) {
  const supabase = createSupabaseBrowserClient()

  if (!supabase) {
    throw new Error('Supabase is not configured. Please set up your environment variables.')
  }

  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })

  if (error) {
    throw new Error(error.message)
  }

  return data
}

export async function signUpWithEmail(
  email: string,
  password: string,
  fullName: string
) {
  const supabase = createSupabaseBrowserClient()

  if (!supabase) {
    throw new Error('Supabase is not configured. Please set up your environment variables.')
  }

  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: fullName,
      },
    },
  })

  if (error) {
    throw new Error(error.message)
  }

  return data
}

export async function resetPassword(email: string) {
  const supabase = createSupabaseBrowserClient()

  if (!supabase) {
    throw new Error('Supabase is not configured. Please set up your environment variables.')
  }

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/auth/reset-password`,
  })

  if (error) {
    throw new Error(error.message)
  }
}

export async function updatePassword(password: string) {
  const supabase = createSupabaseBrowserClient()

  if (!supabase) {
    throw new Error('Supabase is not configured. Please set up your environment variables.')
  }

  const { error } = await supabase.auth.updateUser({
    password,
  })

  if (error) {
    throw new Error(error.message)
  }
}

// Role-based access control
export function hasRole(user: User | null, roles: string[]): boolean {
  if (!user) return false
  return roles.includes(user.role)
}

export function isAdmin(user: User | null): boolean {
  return hasRole(user, ['admin'])
}

export function isManager(user: User | null): boolean {
  return hasRole(user, ['admin', 'manager'])
}

export function canAccessAdminPanel(user: User | null): boolean {
  return hasRole(user, ['admin', 'manager'])
}
