/**
 * Account Settings Page - User Profile and Preferences
 * 
 * Comprehensive account management interface for LibyanoEx shipping portal users
 * 
 * Features:
 * - Profile information management
 * - Subscription plan management
 * - Notification preferences
 * - Security settings (password, 2FA)
 * - Billing and payment methods
 * - Account activity and audit log
 * - Data export and account deletion
 * - Shipping preferences and defaults
 * 
 * Sections:
 * - Profile - Personal information and contact details
 * - Subscription - Plan management and billing
 * - Security - Password, 2FA, and security settings
 * - Notifications - Email and SMS preferences
 * - Shipping - Default addresses and preferences
 * - Privacy - Data management and account controls
 */

'use client'

import { useState } from 'react'
import { useAuth } from '@/hooks/use-auth'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  User, 
  Mail, 
  Phone, 
  CreditCard, 
  Shield, 
  Bell, 
  MapPin,
  Download,
  Trash2,
  Edit,
  Save,
  X,
  Check,
  Crown,
  Lock,
  Eye,
  EyeOff,
  Smartphone,
  Globe,
  Settings,
  AlertTriangle,
  Info
} from 'lucide-react'

// Types for account settings
interface UserProfile {
  firstName: string
  lastName: string
  email: string
  phone: string
  company?: string
  avatar?: string
}

interface SubscriptionInfo {
  plan: 'basic' | 'standard' | 'premium'
  status: 'active' | 'cancelled' | 'past_due'
  nextBilling: string
  amount: number
  packagesUsed: number
  packagesLimit: number
}

interface NotificationPreferences {
  emailNotifications: {
    packageUpdates: boolean
    promotions: boolean
    billing: boolean
    security: boolean
  }
  smsNotifications: {
    packageDelivered: boolean
    importantUpdates: boolean
  }
  pushNotifications: {
    enabled: boolean
    packageUpdates: boolean
  }
}

interface SecuritySettings {
  twoFactorEnabled: boolean
  lastPasswordChange: string
  activeSessions: number
}

export default function AccountSettingsPage() {
  const { user } = useAuth()
  
  // State management
  const [activeTab, setActiveTab] = useState<'profile' | 'subscription' | 'security' | 'notifications' | 'shipping' | 'privacy'>('profile')
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  
  // Form states
  const [profile, setProfile] = useState<UserProfile>({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Tech Solutions Inc'
  })

  const [subscription] = useState<SubscriptionInfo>({
    plan: 'standard',
    status: 'active',
    nextBilling: '2025-02-13',
    amount: 19.99,
    packagesUsed: 12,
    packagesLimit: 50
  })

  const [notifications, setNotifications] = useState<NotificationPreferences>({
    emailNotifications: {
      packageUpdates: true,
      promotions: false,
      billing: true,
      security: true
    },
    smsNotifications: {
      packageDelivered: true,
      importantUpdates: true
    },
    pushNotifications: {
      enabled: true,
      packageUpdates: true
    }
  })

  const [security] = useState<SecuritySettings>({
    twoFactorEnabled: false,
    lastPasswordChange: '2024-12-01',
    activeSessions: 3
  })

  /**
   * Handles profile save
   */
  const handleSaveProfile = async () => {
    setIsSaving(true)
    try {
      // TODO: Implement profile save API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setIsEditing(false)
    } catch (error) {
      console.error('Error saving profile:', error)
    } finally {
      setIsSaving(false)
    }
  }

  /**
   * Handles notification preference changes
   */
  const handleNotificationChange = (category: keyof NotificationPreferences, setting: string, value: boolean) => {
    setNotifications(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [setting]: value
      }
    }))
  }

  /**
   * Gets subscription plan details
   */
  const getSubscriptionPlanDetails = (plan: string) => {
    const plans = {
      basic: { name: 'Basic', color: 'bg-blue-100 text-blue-800', icon: Package },
      standard: { name: 'Standard', color: 'bg-purple-100 text-purple-800', icon: Truck },
      premium: { name: 'Premium', color: 'bg-gold-100 text-gold-800', icon: Crown }
    }
    return plans[plan as keyof typeof plans] || plans.basic
  }

  /**
   * Formats currency
   */
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  /**
   * Formats date
   */
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const planDetails = getSubscriptionPlanDetails(subscription.plan)

  // Tab navigation
  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'subscription', label: 'Subscription', icon: CreditCard },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'shipping', label: 'Shipping', icon: MapPin },
    { id: 'privacy', label: 'Privacy', icon: Settings }
  ] as const

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Account Settings
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your profile, subscription, and preferences
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Navigation Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <CardContent className="p-0">
                <nav className="space-y-1">
                  {tabs.map((tab) => {
                    const TabIcon = tab.icon
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`w-full flex items-center space-x-3 px-4 py-3 text-left transition-colors ${
                          activeTab === tab.id
                            ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 border-r-2 border-blue-600'
                            : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800'
                        }`}
                      >
                        <TabIcon className="h-5 w-5" />
                        <span className="font-medium">{tab.label}</span>
                      </button>
                    )
                  })}
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Profile Tab */}
            {activeTab === 'profile' && (
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center space-x-2">
                        <User className="h-5 w-5 text-blue-600" />
                        <span>Profile Information</span>
                      </CardTitle>
                      <CardDescription>
                        Update your personal information and contact details
                      </CardDescription>
                    </div>
                    {!isEditing ? (
                      <Button variant="outline" onClick={() => setIsEditing(true)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                    ) : (
                      <div className="flex space-x-2">
                        <Button variant="outline" onClick={() => setIsEditing(false)}>
                          <X className="h-4 w-4 mr-2" />
                          Cancel
                        </Button>
                        <Button onClick={handleSaveProfile} disabled={isSaving}>
                          {isSaving ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Saving...
                            </>
                          ) : (
                            <>
                              <Save className="h-4 w-4 mr-2" />
                              Save
                            </>
                          )}
                        </Button>
                      </div>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Profile Picture */}
                  <div className="flex items-center space-x-4">
                    <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                      {profile.firstName.charAt(0)}{profile.lastName.charAt(0)}
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {profile.firstName} {profile.lastName}
                      </h3>
                      <p className="text-sm text-gray-500">{profile.email}</p>
                      {isEditing && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2"
                        >
                          Change Photo
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Profile Form */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">First Name</Label>
                      <Input
                        id="firstName"
                        value={profile.firstName}
                        onChange={(e) => setProfile(prev => ({ ...prev, firstName: e.target.value }))}
                        disabled={!isEditing}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input
                        id="lastName"
                        value={profile.lastName}
                        onChange={(e) => setProfile(prev => ({ ...prev, lastName: e.target.value }))}
                        disabled={!isEditing}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        value={profile.email}
                        onChange={(e) => setProfile(prev => ({ ...prev, email: e.target.value }))}
                        disabled={!isEditing}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={profile.phone}
                        onChange={(e) => setProfile(prev => ({ ...prev, phone: e.target.value }))}
                        disabled={!isEditing}
                      />
                    </div>
                    
                    <div className="space-y-2 md:col-span-2">
                      <Label htmlFor="company">Company (Optional)</Label>
                      <Input
                        id="company"
                        value={profile.company || ''}
                        onChange={(e) => setProfile(prev => ({ ...prev, company: e.target.value }))}
                        disabled={!isEditing}
                        placeholder="Your company name"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Other tabs placeholder */}
            {activeTab !== 'profile' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    {activeTab === 'subscription' && <CreditCard className="h-5 w-5 text-purple-600" />}
                    {activeTab === 'security' && <Shield className="h-5 w-5 text-green-600" />}
                    {activeTab === 'notifications' && <Bell className="h-5 w-5 text-orange-600" />}
                    {activeTab === 'shipping' && <MapPin className="h-5 w-5 text-blue-600" />}
                    {activeTab === 'privacy' && <Settings className="h-5 w-5 text-gray-600" />}
                    <span>
                      {activeTab === 'subscription' && 'Subscription & Billing'}
                      {activeTab === 'security' && 'Security Settings'}
                      {activeTab === 'notifications' && 'Notification Preferences'}
                      {activeTab === 'shipping' && 'Shipping Preferences'}
                      {activeTab === 'privacy' && 'Privacy & Data'}
                    </span>
                  </CardTitle>
                  <CardDescription>
                    {activeTab === 'subscription' && 'Manage your subscription plan and billing information'}
                    {activeTab === 'security' && 'Manage your account security and authentication'}
                    {activeTab === 'notifications' && 'Choose how you want to be notified about your shipments'}
                    {activeTab === 'shipping' && 'Manage your default shipping settings and preferences'}
                    {activeTab === 'privacy' && 'Control your data and privacy settings'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                      {activeTab === 'subscription' && <CreditCard className="h-8 w-8 text-purple-600" />}
                      {activeTab === 'security' && <Shield className="h-8 w-8 text-green-600" />}
                      {activeTab === 'notifications' && <Bell className="h-8 w-8 text-orange-600" />}
                      {activeTab === 'shipping' && <MapPin className="h-8 w-8 text-blue-600" />}
                      {activeTab === 'privacy' && <Settings className="h-8 w-8 text-gray-600" />}
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      {activeTab === 'subscription' && 'Subscription Management'}
                      {activeTab === 'security' && 'Security Settings'}
                      {activeTab === 'notifications' && 'Notification Preferences'}
                      {activeTab === 'shipping' && 'Shipping Preferences'}
                      {activeTab === 'privacy' && 'Privacy Controls'}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                      {activeTab === 'subscription' && 'Plan management, billing history, and subscription controls will be available here.'}
                      {activeTab === 'security' && 'Password management, two-factor authentication, and security settings will be available here.'}
                      {activeTab === 'notifications' && 'Email, SMS, and push notification preferences will be available here.'}
                      {activeTab === 'shipping' && 'Default addresses, carrier preferences, and shipping options will be available here.'}
                      {activeTab === 'privacy' && 'Data export, privacy controls, and account deletion options will be available here.'}
                    </p>
                    <Badge className="text-sm bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
                      Coming Soon
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
